import * as THREE from 'three';

export class WaterPlane {
	constructor(width, height) {
		this.width = width;
		this.height = height;
		this.water = null;
		this.sky = null;
		this.sun = new THREE.Vector3();
		this.waterNormals2 = null; // 存储第二个法线贴图引用
		this.originalNormalMap = null; // 存储原始法线贴图引用
	}

	createWaterPlane() {
		const waterGeometry = new THREE.PlaneGeometry(10000, 10000);

		// 加载水波纹理作为法线贴图
		const waterNormals = new THREE.TextureLoader().load('/waternormals.jpg');
		waterNormals.wrapS = waterNormals.wrapT = THREE.RepeatWrapping;
		// 使用更自然的重复次数，避免过于规律的图案
		waterNormals.repeat.set(
			200 + Math.random() * 200,
			200 + Math.random() * 200
		);

		// 创建第二个法线贴图用于叠加效果
		this.waterNormals2 = waterNormals.clone();
		// 第二层使用完全不同的随机重复次数，创建更复杂的波纹
		this.waterNormals2.repeat.set(
			120 + Math.random() * 180,
			120 + Math.random() * 180
		);

		// 保存原始法线贴图引用
		this.originalNormalMap = waterNormals;

		// 使用物理材质创建更真实的水面效果
		const waterMaterial = new THREE.MeshStandardMaterial({
			color: 0x2a4a6a, // 调整为更自然的蓝色
			normalMap: waterNormals,
			transparent: true,
			opacity: 0.8,
			roughness: 0.7, // 更低的粗糙度，增强反射效果
			metalness: 0, // 非金属
			normalScale: new THREE.Vector2(
				0.2 + Math.random() * 0.2,
				0.2 + Math.random() * 0.2
			), // 随机法线强度
			side: THREE.DoubleSide,
		});

		// 创建简单的水面网格
		this.water = new THREE.Mesh(waterGeometry, waterMaterial);
		this.water.rotation.x = -Math.PI / 2;
		this.water.position.y = -7.5; // 稍微提高水面位置，使其更明显

		// 设置阴影
		this.water.castShadow = false; // 水面不投射阴影
		this.water.receiveShadow = true; // 水面接收阴影

		// 设置动画更新所需的时间属性
		this.water.userData.time = 0;

		return this.water;
	}

	// 更新水面动画（需要在渲染循环中调用）
	update(time) {
		if (this.water && this.water.material && this.water.material.normalMap) {
			// 使用多层噪声函数创建更自然的波纹运动
			const slowTime = time * 0.02;
			const mediumTime = time * 0.053;
			const fastTime = time * 0.127;
			const verySlowTime = time * 0.007;
			const chaosTime = time * 0.231;

			// 添加更多随机性的噪声层
			const noise1 = Math.sin(time * 0.017) * Math.cos(time * 0.041);
			const noise2 = Math.cos(time * 0.029) * Math.sin(time * 0.067);
			const noise3 = Math.sin(time * 0.083) * Math.cos(time * 0.019);

			// 主法线贴图偏移 - 使用更多频率叠加创建复杂运动
			this.water.material.normalMap.offset.x =
				Math.sin(slowTime * 1.37) * 0.12 +
				Math.sin(mediumTime * 2.73) * 0.08 +
				Math.sin(fastTime * 0.89) * 0.04 +
				Math.sin(verySlowTime * 3.14) * 0.15 +
				Math.cos(chaosTime * 1.61) * 0.06 +
				noise1 * 0.03 +
				time * 0.018;

			this.water.material.normalMap.offset.y =
				Math.cos(slowTime * 1.19) * 0.1 +
				Math.cos(mediumTime * 2.41) * 0.07 +
				Math.cos(fastTime * 1.53) * 0.05 +
				Math.sin(verySlowTime * 2.67) * 0.13 +
				Math.sin(chaosTime * 0.97) * 0.04 +
				noise2 * 0.025 +
				time * 0.012;

			// 更新第二个法线贴图，使用不同的频率组合
			if (this.waterNormals2) {
				this.waterNormals2.offset.x =
					Math.cos(slowTime * 1.47) * 0.09 +
					Math.sin(mediumTime * 1.83) * 0.06 +
					Math.cos(fastTime * 0.71) * 0.03 +
					Math.sin(verySlowTime * 4.19) * 0.11 +
					Math.cos(chaosTime * 2.31) * 0.05 +
					noise3 * 0.04 -
					time * 0.014;

				this.waterNormals2.offset.y =
					Math.sin(slowTime * 1.93) * 0.08 +
					Math.cos(mediumTime * 1.27) * 0.05 +
					Math.sin(fastTime * 2.11) * 0.04 +
					Math.cos(verySlowTime * 3.67) * 0.12 +
					Math.sin(chaosTime * 1.43) * 0.06 +
					noise1 * noise2 * 0.03 +
					time * 0.019;

				// 使用更复杂的混合函数，避免规律性切换
				const blendNoise =
					Math.sin(time * 0.011) * 0.25 +
					Math.sin(time * 0.037) * 0.2 +
					Math.sin(time * 0.059) * 0.15 +
					Math.cos(time * 0.023) * 0.1 +
					Math.cos(time * 0.047) * 0.08 +
					(noise1 + noise2 + noise3) * 0.05;
				const blendFactor = (blendNoise + 1) * 0.5;

				// 使用更宽的渐变混合区间，减少切换频率
				if (blendFactor > 0.7) {
					this.water.material.normalMap = this.waterNormals2;
				} else if (blendFactor < 0.3) {
					this.water.material.normalMap = this.originalNormalMap;
				}
				// 在0.3-0.7之间保持当前贴图，大幅减少频繁切换
			}

			// 动态调整法线强度，创建更自然的波纹变化
			const normalIntensity =
				0.2 +
				Math.sin(time * 0.017) * 0.12 +
				Math.cos(time * 0.031) * 0.08 +
				Math.sin(time * 0.053) * 0.06 +
				Math.cos(time * 0.071) * 0.04 +
				noise1 * 0.03;
			this.water.material.normalScale.set(normalIntensity, normalIntensity);

			// 保存时间值
			this.water.userData.time = time;
		}
	}

	// 设置水面颜色
	setWaterColor(color) {
		if (this.water && this.water.material) {
			this.water.material.color.setHex(color);
		}
	}

	// 设置水面透明度
	setWaterOpacity(opacity) {
		if (this.water && this.water.material) {
			this.water.material.transparent = opacity < 1.0;
			this.water.material.opacity = opacity;
		}
	}

	// 预设舒适的蓝灰色
	setComfortableColor() {
		this.setWaterColor(0x4a6b8a); // 柔和的蓝灰色
		this.setWaterOpacity(0.8);
	}

	// 预设深蓝色
	setDeepBlue() {
		this.setWaterColor(0x2c5f7c); // 深蓝色
		this.setWaterOpacity(0.85);
	}

	// 预设浅蓝色
	setLightBlue() {
		this.setWaterColor(0x6b9dc2); // 浅蓝色
		this.setWaterOpacity(0.75);
	}
}
